dependency "kms" {
  config_path = "../kms"
}

inputs = {
  aws_region = "ca-central-1"
  environment = "prod"
  country = "ca"
  bucket_name = "lambdas"
  # TODO: Provide PROD customer objects
  customer_config = null
  
  s3_lifecycle_noncurrent_version_expiration_days = 30
  
  tags = merge(
    include.root.locals.tags,
    {
      ChargeCode  = "03SMAL.GENN.0000.WA9.DEV"
      country     = "ca"
      environment = "prod"
      Module      = "s3"
    }
  )
}

terraform {
  source = "../../../../modules/bucket/"
}

include "root" {
  expose = true
  path   = find_in_parent_folders()
}
