dependency "kms" {
  config_path = "../kms"
  mock_outputs = {
    kms_key_arns = {
      "washingtoncounty-mn" = "arn:aws:kms:us-east-1:123456789012:key/mock-key-id"
      "flagler" = "arn:aws:kms:us-east-1:123456789012:key/mock-key-id"
      "pvgt" = "arn:aws:kms:us-east-1:123456789012:key/mock-key-id"
    }
  }
}

inputs = {
  aws_region = "us-east-1"
  environment = "qa"
  country = "us"
  bucket_name = "lambdas"
  customer_config = {
    for customer in ["washingtoncounty-mn", "flagler", "pvgt"] : customer => {
      enabled = true
      kms_key_arn = dependency.kms.outputs.kms_key_arns[customer]
      customer_name = customer
    }
  }
  
  s3_lifecycle_noncurrent_version_expiration_days = 30
  
  tags = merge(
    include.root.locals.tags,
    {
      ChargeCode  = "03SMAL.GENN.0000.WA9.DEV"
      country     = "us"
      environment = "qa"
      Module      = "s3"
    }
  )
}

terraform {
  source = "../../../../modules/bucket/"
}

include "root" {
  expose = true
  path   = find_in_parent_folders()
}
