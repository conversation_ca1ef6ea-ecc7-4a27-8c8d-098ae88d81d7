# Comtech SmartAnalytics Infrastructure

This repository contains Terraform modules and infrastructure as code for the SmartAnalytics platform.

## Repository Structure

- **Deployment**: Terragrunt configurations for different environments
- **Environments**: Environment-specific configurations (dev, qa, prod)
- **Infrastructure**: Reusable Terraform modules for AWS resources

## Architecture Overview

The SmartAnalytics infrastructure follows a modular approach with customer-specific resource isolation:

- **Customer Isolation**: Each customer gets dedicated KMS keys and S3 buckets
- **Multi-Environment**: Support for dev, qa, and prod environments
- **Multi-Region**: Support for US and Canada regions
- **Security**: Customer-managed KMS encryption for all data at rest
- **Cost Optimization**: Lifecycle policies and resource management

## Core Modules

### Security & Encryption
- **[KMS Module](business_logic_iac/modules/kms/README.md)**: Customer-specific KMS keys for data encryption
- **[Bucket Module](business_logic_iac/modules/bucket/README.md)**: Generic S3 buckets with customer isolation

### Infrastructure
- **[Network Module](business_logic_iac/modules/network/README.md)**: VPC and networking resources
- **[DNS Module](business_logic_iac/modules/dns/README.md)**: Route53 DNS and SSL certificates
- **[Business Logic Module](business_logic_iac/modules/business-logic/README.md)**: Core application infrastructure

### Power BI Integration
- **[Power BI Gateway Module](business_logic_iac/modules/power-bi-gateway/README.md)**: Data gateway for Power BI
- **[Power BI Desktop Module](business_logic_iac/modules/power-bi-desktop/README.md)**: Power BI desktop instances

### Access & Security
- **[Client VPN Module](business_logic_iac/modules/client-vpn/README.md)**: SAML-based VPN access

## Quick Start

### Prerequisites
- AWS CLI configured with appropriate permissions
- Terraform >= 1.0
- Terragrunt >= 0.35
- Valid AWS account with required service quotas

### Deployment Order

For new environments, deploy modules in this order:

1. **KMS** - Customer encryption keys (no dependencies)
2. **Network** - VPC and networking (no dependencies)
3. **DNS** - Route53 and certificates (no dependencies)
4. **Bucket** - S3 buckets (depends on KMS)
5. **Business Logic** - Core application (depends on Network, DNS, Bucket)
6. **Power BI Gateway** - Data gateway (depends on Network)
7. **Power BI Desktop** - Desktop instances (depends on Network)
8. **Client VPN** - VPN access (depends on Network)
