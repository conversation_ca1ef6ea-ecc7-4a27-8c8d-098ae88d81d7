inputs = {
  aws_region = "us-east-1"
  environment = "qa"
  country = "us"
  customer_config = {
    "washingtoncounty-mn" = {
      name = "washingtoncounty-mn"
      enabled = true
    }
    "flagler" = {
      name = "flagler"
      enabled = true
    }
    "pvgt" = {
      name = "pvgt"
      enabled = true
    }
  }
  
  tags = merge(
    include.root.locals.tags,
    {
      ChargeCode  = "03SMAL.GENN.0000.WA9.DEV"
      country     = "us"
      environment = "qa"
      Module      = "kms"
    }
  )
}

terraform {
  source = "../../../../modules/kms/"
}

include "root" {
  expose = true
  path   = find_in_parent_folders()
}
