variable "aws_region" {
  description = "AWS Region"
  type        = string
}

variable "environment" {
  description = "Environment (dev, qa, prod)"
  type        = string
}

variable "country" {
  description = "Country code (us, ca)"
  type        = string
  validation {
    condition     = contains(["ca", "us"], var.country)
    error_message = "Valid value is one of the following: ca OR us."
  }
}

variable "customer_config" {
  description = "Map of customer names to customer objects"
  type        = map(object({
    name      = string
    enabled   = bool 
  }))
}

variable "tags" {
  type        = map(string)
  description = "Map of key/value pairs to apply as tags to all resources"
  default     = {}
}
