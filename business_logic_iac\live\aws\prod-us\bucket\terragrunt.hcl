dependency "kms" {
  config_path = "../kms"
}

inputs = {
  aws_region = "us-east-1"
  environment = "prod"
  country = "us"
  bucket_name = "lambdas"
  customer_config = null
  
  s3_lifecycle_noncurrent_version_expiration_days = 30
  
  tags = merge(
    include.root.locals.tags,
    {
      ChargeCode  = "03SMAL.GENN.0000.WA9.DEV"
      country     = "us"
      environment = "prod"
      Module      = "s3"
    }
  )
}

terraform {
  source = "../../../../modules/bucket/"
}

include "root" {
  expose = true
  path   = find_in_parent_folders()
}
