dependency "kms" {
  config_path = "../kms"
}


inputs = {
  aws_region = "us-east-1"
  environment = "dev"
  country = "us"
  bucket_name = "lambdas"
  customer_config = {
    "washingtoncounty-mn" = {
      enabled = true
      kms_key_arn = dependency.kms.outputs.kms_key_arns["washingtoncounty-mn"]
      customer_name = "washingtoncounty-mn"
    }
    "flagler" = {
      enabled = true
      kms_key_arn = dependency.kms.outputs.kms_key_arns["flagler"]
      customer_name = "flagler"
    }
    "pvgt" = {
      enabled = true
      kms_key_arn = dependency.kms.outputs.kms_key_arns["pvgt"]
      customer_name = "pvgt"
    }
  }

  s3_lifecycle_noncurrent_version_expiration_days = 30

  tags = merge(
    include.root.locals.tags,
    {
      ChargeCode  = "03SMAL.GENN.0000.WA9.DEV"
      country     = "us"
      environment = "dev"
      Module      = "s3"
    }
  )
}

terraform {
  source = "../../../../modules/bucket/"
}

include "root" {
  expose = true
  path   = find_in_parent_folders()
}
